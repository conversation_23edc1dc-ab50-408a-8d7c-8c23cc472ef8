#!/usr/bin/env python3

import sys
import os

# Add the parent directory to Python path so we can import the addon as a package
parent_path = os.path.join(os.path.dirname(__file__), 'output', 'MustardSimplify-2025.2.0')
sys.path.insert(0, parent_path)

print(f"Testing import from: {parent_path}")

try:
    # Test importing the MustardSimplify package
    import MustardSimplify
    print("✓ Import of MustardSimplify package successful")
    
    # Test if steel_lib functions are available
    if hasattr(MustardSimplify, 'add_numbers'):
        print(f"✓ steel_lib functions available: add_numbers(5, 7) = {MustardSimplify.add_numbers(5, 7)}")
    else:
        print("✗ steel_lib functions not available in MustardSimplify module")
        print(f"Available attributes: {[attr for attr in dir(MustardSimplify) if not attr.startswith('_')]}")
        
except ImportError as e:
    print(f"✗ Import of MustardSimplify failed: {e}")
    import traceback
    traceback.print_exc()

# Test importing submodules
try:
    from MustardSimplify import settings
    print("✓ Import of MustardSimplify.settings successful")
except ImportError as e:
    print(f"✗ Import of MustardSimplify.settings failed: {e}")
    import traceback
    traceback.print_exc()
