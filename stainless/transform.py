import ast
import sys
import hashlib
import copy
import shutil
import os

# Mapping from AST operator types to function names from math.py
OP_TO_FUNC = {
    ast.Add: 'add_numbers',
    ast.Sub: 'subtract_numbers',
    ast.Mult: 'multiply_numbers',
    ast.Div: 'divide_numbers',
    ast.Mod: 'modulo_numbers',
    ast.Pow: 'exponentiate_numbers',
    ast.FloorDiv: 'floor_divide_numbers',
    ast.USub: 'negate_number',
}

BOOL_OP_TO_FUNC = {}

COMP_OP_MAP = {
    ast.Eq: 'are_equal',
    ast.NotEq: 'are_not_equal',
    ast.Lt: 'is_less_than',
    ast.LtE: 'is_less_or_equal',
    ast.Gt: 'is_greater_than',
    ast.GtE: 'is_greater_or_equal',
}

# --- Mappings for AST nodes to symbols ---
OP_SYMBOLS = {
    ast.Add: '+', ast.Sub: '-', ast.Mult: '*', ast.Div: '/', ast.Mod: '%',
    ast.Pow: '**', ast.FloorDiv: '//'
}
COMP_OP_SYMBOLS = {
    ast.Eq: '==', ast.NotEq: '!=', ast.Lt: '<', ast.LtE: '<=', ast.Gt: '>',
    ast.GtE: '>=', ast.Is: 'is', ast.IsNot: 'is not'
}

class MathTransformer(ast.NodeTransformer):
    def __init__(self, extended_module_name):
        self.new_functions = {}
        self.extended_module_name = extended_module_name
        self.specialized_functions_used = False

    @staticmethod
    def _is_literal(node):
        """A node is considered a literal if it's a constant value."""
        return isinstance(node, ast.Constant)

    @staticmethod
    def _sanitize_literal_for_func_name(node):
        """Generates a safe and descriptive function name suffix from a literal's value."""
        if not isinstance(node, ast.Constant):
            return "_invalid_literal_"

        val = node.value
        if isinstance(val, bool):
            return str(val).lower()
        if isinstance(val, (int, float)):
            return str(val).replace('.', '_dot_').replace('-', 'neg_')
        elif isinstance(val, str):
            sanitized = ''.join(c if c.isalnum() else '_' for c in val)
            if len(sanitized) > 20:
                hash_part = hashlib.md5(val.encode()).hexdigest()[:8]
                sanitized = sanitized[:12] + '_' + hash_part
            return sanitized
        elif val is None:
            return "None"
        return "_unhandled_literal_"

    def _generate_specialized_function(self, op_type, literal_node, is_literal_left, op_map, symbol_map):
        """Generates a specialized function for an operation with one literal."""
        full_func_name = op_map.get(type(op_type))
        op_symbol = symbol_map.get(type(op_type))

        if not full_func_name or not op_symbol:
            return None

        base_name = full_func_name.replace('_numbers', '').replace('are_', '').replace('is_', '')
        sanitized_literal = self._sanitize_literal_for_func_name(literal_node)
        new_func_name = f"{base_name}_with_{sanitized_literal}"

        if new_func_name not in self.new_functions:
            value_repr = repr(literal_node.value)
            if is_literal_left:
                func_code = f"def {new_func_name}(a):\n    return {value_repr} {op_symbol} a"
            else:
                func_code = f"def {new_func_name}(a):\n    return a {op_symbol} {value_repr}"
            self.new_functions[new_func_name] = func_code
        
        self.specialized_functions_used = True
        return new_func_name

    def visit_BinOp(self, node):
        left_is_literal = self._is_literal(node.left)
        right_is_literal = self._is_literal(node.right)

        if left_is_literal != right_is_literal:
            literal_node = node.left if left_is_literal else node.right
            
            # If the literal is a string, do not transform this operation.
            if isinstance(literal_node.value, str):
                self.generic_visit(node)
                return node

            variable_node = node.right if left_is_literal else node.left
            is_literal_left = left_is_literal

            new_func_name = self._generate_specialized_function(node.op, literal_node, is_literal_left, OP_TO_FUNC, OP_SYMBOLS)
            if new_func_name:
                transformed_variable_node = self.visit(variable_node)
                new_call = ast.Call(func=ast.Name(id=new_func_name, ctx=ast.Load()), args=[transformed_variable_node], keywords=[])
                ast.copy_location(new_call, node)
                return new_call

        self.generic_visit(node)
        func_name = OP_TO_FUNC.get(type(node.op))
        if func_name:
            return ast.Call(func=ast.Name(id=func_name, ctx=ast.Load()), args=[node.left, node.right], keywords=[])
        return node

    def visit_AugAssign(self, node):
        # Check if the value being assigned is a literal
        if self._is_literal(node.value):
            # Do not transform AugAssign with string literals
            if isinstance(node.value.value, str):
                self.generic_visit(node)
                return node
            
            # Generate a specialized function for the operation (e.g., ast.Add for +=)
            new_func_name = self._generate_specialized_function(node.op, node.value, False, OP_TO_FUNC, OP_SYMBOLS)
            
            if new_func_name:
                # The variable being assigned to (e.g., 'x' in 'x += 1' or 'obj.x' in 'obj.x += 1')
                target_node = node.target

                # Create a copy of the target node to be used as the argument for the function call.
                # This is crucial because the original target has a 'Store' context, but when used as
                # an argument, it needs a 'Load' context.
                arg_node = copy.deepcopy(target_node)
                # This walk ensures all parts of the node (e.g. in obj.x) are set to Load context.
                for sub_node in ast.walk(arg_node):
                    if hasattr(sub_node, 'ctx'):
                        sub_node.ctx = ast.Load()

                # Create a call to the new specialized function, e.g., add_with_1_0(obj.location.x)
                call_node = ast.Call(
                    func=ast.Name(id=new_func_name, ctx=ast.Load()),
                    args=[arg_node],
                    keywords=[]
                )

                # Replace the AugAssign node with a standard Assign node, e.g., obj.location.x = ...
                new_assign_node = ast.Assign(targets=[target_node], value=call_node)
                ast.copy_location(new_assign_node, node)
                return new_assign_node

        # If it's not a literal or something goes wrong, visit children and return as is
        self.generic_visit(node)
        return node

    def visit_UnaryOp(self, node):
        self.generic_visit(node)
        func_name = OP_TO_FUNC.get(type(node.op))
        if func_name:
            return ast.Call(func=ast.Name(id=func_name, ctx=ast.Load()), args=[node.operand], keywords=[])
        return node

    def visit_BoolOp(self, node):
        self.generic_visit(node)
        func_name = BOOL_OP_TO_FUNC.get(type(node.op))
        if func_name:
            result_node = node.values[0]
            for i in range(1, len(node.values)):
                result_node = ast.Call(func=ast.Name(id=func_name, ctx=ast.Load()), args=[result_node, node.values[i]], keywords=[])
            ast.copy_location(result_node, node)
            return result_node
        return node

    def visit_Compare(self, node):
        if len(node.ops) == 1:
            op = node.ops[0]
            left_node, right_node = node.left, node.comparators[0]
            left_is_literal = self._is_literal(left_node)
            right_is_literal = self._is_literal(right_node)

            if left_is_literal != right_is_literal:
                literal_node = left_node if left_is_literal else right_node
                
                # Do not transform comparisons with string literals
                if isinstance(literal_node.value, str):
                    self.generic_visit(node)
                    return node
                
                variable_node = right_node if left_is_literal else left_node
                is_literal_left = left_is_literal

                new_func_name = self._generate_specialized_function(op, literal_node, is_literal_left, COMP_OP_MAP, COMP_OP_SYMBOLS)
                if new_func_name:
                    transformed_variable_node = self.visit(variable_node)
                    new_call = ast.Call(func=ast.Name(id=new_func_name, ctx=ast.Load()), args=[transformed_variable_node], keywords=[])
                    ast.copy_location(new_call, node)
                    return new_call

            self.generic_visit(node)
            func_name = COMP_OP_MAP.get(type(op))
            if func_name:
                return ast.Call(func=ast.Name(id=func_name, ctx=ast.Load()), args=[node.left, node.comparators[0]], keywords=[])
            return node

        else:
            bool_op_values = []
            left = node.left
            for i, op in enumerate(node.ops):
                right = node.comparators[i]
                simple_comp_node = ast.Compare(left=left, ops=[op], comparators=[right])
                transformed_comp = self.visit(simple_comp_node)
                bool_op_values.append(transformed_comp)
                left = right

            if len(bool_op_values) > 1:
                result_node = ast.BoolOp(op=ast.And(), values=bool_op_values)
                ast.copy_location(result_node, node)
                return result_node
            
            return bool_op_values[0] if bool_op_values else node

def transform_code(source, transformer, extended_module_name, relative_path):
    """Transforms the source code and adds necessary imports."""
    tree = ast.parse(source)
    transformed_tree = transformer.visit(tree)

    # Find the correct position to insert imports (after any __future__ imports and docstrings)
    insert_pos = 0
    for i, node in enumerate(transformed_tree.body):
        if isinstance(node, ast.ImportFrom) and node.module == '__future__':
            insert_pos = i + 1
        elif isinstance(node, (ast.Import, ast.ImportFrom)):
            insert_pos = i + 1
        # Docstrings are ast.Expr, so we continue past them.
        elif isinstance(node, ast.Expr):
            insert_pos = i + 1
        else: # First non-import/docstring statement
            break

    # Calculate import level from relative_path using pathlib for robustness
    from pathlib import Path
    p = Path(relative_path)
    # The number of parents gives the depth. 'a/b.py' has parents 'a' and '.', so depth is 1.
    # 'b.py' has parent '.', so depth is 0.
    depth = len(p.parents) - 1
    import_level = depth + 1

    # Always import the base steel_lib, as any transformation needs it.
    # Use simple relative import - Blender's wheel system will handle the compiled extensions
    import_node_lib = ast.ImportFrom(
        module='steel_lib',
        names=[ast.alias(name='*', store=ast.Load())],
        level=import_level
    )
    transformed_tree.body.insert(insert_pos, import_node_lib)
    insert_pos += 1

    # If specialized functions were created, import them as well.
    if transformer.specialized_functions_used:
        import_node_extended = ast.ImportFrom(
            module=extended_module_name,
            names=[ast.alias(name='*', store=ast.Load())],
            level=1
        )
        transformed_tree.body.insert(insert_pos, import_node_extended)

    ast.fix_missing_locations(transformed_tree)
    return ast.unparse(transformed_tree)

if __name__ == "__main__":
    if len(sys.argv) < 5:
        print(f"Usage: python {sys.argv[0]} <source_file> <output_file> <extended_module_name> <relative_path>", file=sys.stderr)
        sys.exit(1)

    source_file = sys.argv[1]
    output_file = sys.argv[2]
    extended_module_name = sys.argv[3]
    relative_path = sys.argv[4]
    
    try:
        with open(source_file, 'r') as f:
            source = f.read()
        
        transformer = MathTransformer(extended_module_name)
        transformed_code = transform_code(source, transformer, extended_module_name, relative_path)
        
        with open(output_file, 'w') as f:
            f.write(transformed_code)
        
        if transformer.new_functions:
            for func_code in transformer.new_functions.values():
                print("STEEL_FUNC_DEF_START")
                print(func_code)
                print("STEEL_FUNC_DEF_END")

    except FileNotFoundError:
        print(f"Error: Input file not found at {input_file_path}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)
