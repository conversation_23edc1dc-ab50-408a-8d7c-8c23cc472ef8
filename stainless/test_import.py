#!/usr/bin/env python3

import sys
import os

# Add the addon directory to Python path
addon_path = os.path.join(os.path.dirname(__file__), 'output', 'MustardSimplify-2025.2.0', 'MustardSimplify')
sys.path.insert(0, addon_path)

print(f"Testing import from: {addon_path}")
print(f"Files in directory: {os.listdir(addon_path)}")

try:
    # Test importing steel_lib directly
    import steel_lib
    print("✓ Direct import of steel_lib successful")
    print(f"steel_lib.add_numbers(2, 3) = {steel_lib.add_numbers(2, 3)}")
except ImportError as e:
    print(f"✗ Direct import of steel_lib failed: {e}")

try:
    # Test importing from the main module
    import MustardSimplify
    print("✓ Import of MustardSimplify module successful")
    
    # Test if steel_lib functions are available
    if hasattr(MustardSimplify, 'add_numbers'):
        print(f"✓ steel_lib functions available: add_numbers(5, 7) = {MustardSimplify.add_numbers(5, 7)}")
    else:
        print("✗ steel_lib functions not available in MustardSimplify module")
        
except ImportError as e:
    print(f"✗ Import of MustardSimplify failed: {e}")
    import traceback
    traceback.print_exc()
