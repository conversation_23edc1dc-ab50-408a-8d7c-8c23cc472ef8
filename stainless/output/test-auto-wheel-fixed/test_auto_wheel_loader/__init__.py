def load_wheel_modules():
    """Load compiled modules from wheel when installed as traditional addon"""
    from pathlib import Path
    import zipfile
    import sys
    import os
    addon_dir = Path(__file__).parent
    wheels_dir = addon_dir / 'wheels'
    wheel_files = list(wheels_dir.glob('*.whl'))
    if not wheel_files:
        raise ImportError('No wheel files found in wheels directory')
    wheel_path = wheel_files[0]
    extract_dir = addon_dir / '.wheel_cache'
    extract_dir.mkdir(exist_ok=True)
    wheel_name = wheel_path.stem
    wheel_extract_dir = extract_dir / wheel_name
    if not wheel_extract_dir.exists():
        with zipfile.ZipFile(wheel_path, 'r') as wheel_zip:
            wheel_zip.extractall(wheel_extract_dir)
    wheel_extract_str = str(wheel_extract_dir)
    if wheel_extract_str not in sys.path:
        sys.path.insert(0, wheel_extract_str)
    return wheel_extract_str
try:
    from steel_lib import *
except ImportError:
    try:
        wheel_temp_dir = load_wheel_modules()
        import importlib.util
        import sys
        import os
        steel_lib_path = None
        for root, dirs, files in os.walk(wheel_temp_dir):
            for file in files:
                if file.startswith('steel_lib') and file.endswith('.so'):
                    steel_lib_path = os.path.join(root, file)
                    break
            if steel_lib_path:
                break
        if steel_lib_path:
            spec = importlib.util.spec_from_file_location('steel_lib', steel_lib_path)
            steel_lib_module = importlib.util.module_from_spec(spec)
            sys.modules['steel_lib'] = steel_lib_module
            spec.loader.exec_module(steel_lib_module)
            for attr_name in dir(steel_lib_module):
                if not attr_name.startswith('_'):
                    globals()[attr_name] = getattr(steel_lib_module, attr_name)
        else:
            raise ImportError('Could not find steel_lib.so file in extracted wheel')
    except ImportError as ie:
        raise ImportError(f'Failed to load steel_lib from wheel: {ie}')
bl_info = {'name': 'Test Auto Wheel Loader', 'author': 'Steel Test', 'version': (1, 0, 0), 'blender': (4, 0, 0), 'location': 'View3D > Sidebar > Test', 'description': 'Test addon for auto wheel loader functionality', 'category': 'Development'}
import bpy

def test_math_operations():
    """Test function that uses steel_lib math operations"""
    result1 = add_numbers(10, 5)
    result2 = subtract_numbers(20, 3)
    result3 = multiply_numbers(4, 7)
    print(f'Addition: {result1}')
    print(f'Subtraction: {result2}')
    print(f'Multiplication: {result3}')
    return (result1, result2, result3)

class TEST_OT_math_operations(bpy.types.Operator):
    """Test Math Operations"""
    bl_idname = 'test.math_operations'
    bl_label = 'Test Math Operations'

    def execute(self, context):
        results = test_math_operations()
        self.report({'INFO'}, f'Math results: {results}')
        return {'FINISHED'}

class TEST_PT_panel(bpy.types.Panel):
    """Test Panel"""
    bl_label = 'Test Auto Wheel Loader'
    bl_idname = 'TEST_PT_panel'
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Test'

    def draw(self, context):
        layout = self.layout
        layout.operator('test.math_operations')

def register():
    bpy.utils.register_class(TEST_OT_math_operations)
    bpy.utils.register_class(TEST_PT_panel)

def unregister():
    bpy.utils.unregister_class(TEST_OT_math_operations)
    bpy.utils.unregister_class(TEST_PT_panel)
if __name__ == '__main__':
    register()