bl_info = {
    "name": "Test Auto Wheel Loader",
    "author": "Steel Test",
    "version": (1, 0, 0),
    "blender": (4, 0, 0),
    "location": "View3D > Sidebar > Test",
    "description": "Test addon for auto wheel loader functionality",
    "category": "Development",
}

import bpy

def test_math_operations():
    """Test function that uses steel_lib math operations"""
    result1 = 10 + 5  # This will be transformed to add_numbers(10, 5)
    result2 = 20 - 3  # This will be transformed to subtract_numbers(20, 3)
    result3 = 4 * 7   # This will be transformed to multiply_numbers(4, 7)
    
    print(f"Addition: {result1}")
    print(f"Subtraction: {result2}")
    print(f"Multiplication: {result3}")
    
    return result1, result2, result3

class TEST_OT_math_operations(bpy.types.Operator):
    """Test Math Operations"""
    bl_idname = "test.math_operations"
    bl_label = "Test Math Operations"
    
    def execute(self, context):
        results = test_math_operations()
        self.report({'INFO'}, f"Math results: {results}")
        return {'FINISHED'}

class TEST_PT_panel(bpy.types.Panel):
    """Test Panel"""
    bl_label = "Test Auto Wheel Loader"
    bl_idname = "TEST_PT_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Test"
    
    def draw(self, context):
        layout = self.layout
        layout.operator("test.math_operations")

def register():
    bpy.utils.register_class(TEST_OT_math_operations)
    bpy.utils.register_class(TEST_PT_panel)

def unregister():
    bpy.utils.unregister_class(TEST_OT_math_operations)
    bpy.utils.unregister_class(TEST_PT_panel)

if __name__ == "__main__":
    register()
